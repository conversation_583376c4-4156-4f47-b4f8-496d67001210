import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { ButtonComponent } from '../../shared/components/button/button.component';
import { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';
import { ChartComponent } from '../../shared/components/chart/chart.component';
import { TechStackModalComponent } from '../applications/components/tech-stack-modal/tech-stack-modal.component';

interface TechStackItem {
  id: number;
  category: string;
  technology: string;
  version: string;
  purpose: string;
  isCore: boolean;
  applications: number;
  supportLevel: string;
  endOfLife?: Date;
  licenseType?: string;
}

@Component({
  selector: 'app-tech-stack',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    TechStackModalComponent
  ],
  template: `
    <div class="tech-stack-page">
      <div class="page-header">
        <div class="header-content">
          <h1>Technology Stack</h1>
          <p class="page-subtitle">Manage and monitor technology stack across applications</p>
        </div>
        <div class="header-actions">
          <app-button
            variant="primary"
            leftIcon="M12 4v16m8-8H4"
            (clicked)="openAddModal()"
          >
            Add Technology
          </app-button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Technologies">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.categories }} categories</div>
          </div>
        </app-card>

        <app-card title="Core Technologies">
          <div class="stat-content">
            <div class="stat-number core">{{ stats.core }}</div>
            <div class="stat-change">{{ stats.corePercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="End of Life Soon">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.endOfLife }}</div>
            <div class="stat-change">Need replacement</div>
          </div>
        </app-card>

        <app-card title="Applications Using">
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalApplications }}</div>
            <div class="stat-change">Total usage</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Technologies by Category" subtitle="Distribution across categories">
          <app-chart
            type="doughnut"
            [data]="categoryChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Support Level Distribution" subtitle="Technologies by support level">
          <app-chart
            type="bar"
            [data]="supportChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Technology Stack Table -->
      <app-card title="Technology Stack" subtitle="All technologies in use">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search technologies..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('category', $event)">
              <option value="">All Categories</option>
              <option value="frontend">Frontend</option>
              <option value="backend">Backend</option>
              <option value="database">Database</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="devops">DevOps</option>
              <option value="testing">Testing</option>
              <option value="monitoring">Monitoring</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('supportLevel', $event)">
              <option value="">All Support Levels</option>
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="deprecated">Deprecated</option>
              <option value="end_of_life">End of Life</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredTechStack"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Add/Edit Modal -->
      <app-tech-stack-modal
        [isOpen]="showModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        (closed)="closeModal()"
        (saved)="onTechStackSaved($event)"
      ></app-tech-stack-modal>
    </div>
  `,
  styles: [`
    .tech-stack-page {
      min-height: 100%;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-xl);
    }

    .header-content h1 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0;
      font-size: var(--text-lg);
      color: var(--secondary-600);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .stat-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .stat-number {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);

      &.core {
        color: var(--primary-600);
      }

      &.warning {
        color: var(--warning-600);
      }
    }

    .stat-change {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .charts-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .table-controls {
      margin-bottom: var(--spacing-lg);
    }

    .search-controls {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
    }

    .search-input,
    .filter-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .search-input {
      flex: 1;
      min-width: 200px;
    }

    .filter-select {
      min-width: 150px;
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    @media (max-width: 768px) {
      .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .charts-section {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .search-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .search-input,
      .filter-select {
        min-width: auto;
      }
    }
  `]
})
export class TechStackComponent implements OnInit {
  techStack: TechStackItem[] = [];
  filteredTechStack: TechStackItem[] = [];
  loading = false;

  // Modal state
  showModal = false;
  editMode = false;
  editData: any = null;
  modalLoading = false;

  // Statistics
  stats = {
    total: 0,
    core: 0,
    endOfLife: 0,
    categories: 0,
    totalApplications: 0,
    corePercentage: 0
  };

  // Chart data
  categoryChartData: any = null;
  supportChartData: any = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };

  // Table configuration
  tableColumns: TableColumn[] = [
    { key: 'technology', label: 'Technology', sortable: true },
    { key: 'category', label: 'Category', type: 'badge', sortable: true },
    { key: 'version', label: 'Version', sortable: true },
    { key: 'purpose', label: 'Purpose', sortable: true },
    { key: 'supportLevel', label: 'Support', type: 'badge', sortable: true },
    { key: 'applications', label: 'Apps', type: 'number', sortable: true },
    { key: 'isCore', label: 'Core', type: 'boolean', sortable: true },
    { key: 'licenseType', label: 'License', sortable: true }
  ];

  tableActions: TableAction[] = [
    {
      label: 'Edit',
      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',
      variant: 'ghost',
      action: (item) => this.editTechStack(item)
    },
    {
      label: 'Delete',
      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',
      variant: 'error',
      action: (item) => this.deleteTechStack(item)
    }
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.loadTechStack();
  }

  private loadTechStack(): void {
    this.loading = true;

    // Mock data - replace with actual API call
    setTimeout(() => {
      this.techStack = this.generateMockTechStack();
      this.filteredTechStack = [...this.techStack];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1000);
  }

  private generateMockTechStack(): TechStackItem[] {
    const categories = ['frontend', 'backend', 'database', 'infrastructure', 'devops', 'testing', 'monitoring'];
    const supportLevels = ['active', 'maintenance', 'deprecated', 'end_of_life'];
    const licenses = ['MIT', 'Apache 2.0', 'GPL', 'BSD', 'Commercial', 'Open Source'];

    return Array.from({ length: 30 }, (_, i) => ({
      id: i + 1,
      category: categories[Math.floor(Math.random() * categories.length)],
      technology: `Technology ${i + 1}`,
      version: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      purpose: `Purpose for technology ${i + 1}`,
      isCore: Math.random() > 0.7,
      applications: Math.floor(Math.random() * 15) + 1,
      supportLevel: supportLevels[Math.floor(Math.random() * supportLevels.length)],
      endOfLife: Math.random() > 0.8 ? new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000) : undefined,
      licenseType: licenses[Math.floor(Math.random() * licenses.length)]
    }));
  }

  private updateStats(): void {
    this.stats.total = this.techStack.length;
    this.stats.core = this.techStack.filter(t => t.isCore).length;
    this.stats.endOfLife = this.techStack.filter(t => t.supportLevel === 'end_of_life' || t.supportLevel === 'deprecated').length;
    this.stats.categories = new Set(this.techStack.map(t => t.category)).size;
    this.stats.totalApplications = this.techStack.reduce((sum, t) => sum + t.applications, 0);
    this.stats.corePercentage = Math.round((this.stats.core / this.stats.total) * 100);
  }

  private updateChartData(): void {
    // Category distribution chart
    const categoryCount = this.techStack.reduce((acc, tech) => {
      acc[tech.category] = (acc[tech.category] || 0) + 1;
      return acc;
    }, {} as any);

    this.categoryChartData = {
      labels: Object.keys(categoryCount),
      datasets: [{
        data: Object.values(categoryCount),
        backgroundColor: [
          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6',
          '#06b6d4', '#84cc16'
        ]
      }]
    };

    // Support level distribution chart
    const supportCount = this.techStack.reduce((acc, tech) => {
      acc[tech.supportLevel] = (acc[tech.supportLevel] || 0) + 1;
      return acc;
    }, {} as any);

    this.supportChartData = {
      labels: Object.keys(supportCount),
      datasets: [{
        label: 'Technologies',
        data: Object.values(supportCount),
        backgroundColor: ['#22c55e', '#f59e0b', '#f97316', '#ef4444']
      }]
    };
  }

  openAddModal(): void {
    this.editMode = false;
    this.editData = null;
    this.showModal = true;
  }

  editTechStack(techStack: TechStackItem): void {
    this.editMode = true;
    this.editData = { ...techStack };
    this.showModal = true;
  }

  deleteTechStack(techStack: TechStackItem): void {
    if (confirm(`Are you sure you want to delete ${techStack.technology}?`)) {
      this.techStack = this.techStack.filter(t => t.id !== techStack.id);
      this.filteredTechStack = this.filteredTechStack.filter(t => t.id !== techStack.id);
      this.updateStats();
      this.updateChartData();
    }
  }

  closeModal(): void {
    this.showModal = false;
    this.editMode = false;
    this.editData = null;
  }

  onTechStackSaved(techStackData: any): void {
    this.modalLoading = true;

    setTimeout(() => {
      if (this.editMode) {
        const index = this.techStack.findIndex(t => t.id === this.editData.id);
        if (index !== -1) {
          this.techStack[index] = { ...this.techStack[index], ...techStackData };
        }
      } else {
        const newTechStack: TechStackItem = {
          id: Math.max(...this.techStack.map(t => t.id)) + 1,
          ...techStackData,
          applications: 0
        };
        this.techStack.push(newTechStack);
      }

      this.filteredTechStack = [...this.techStack];
      this.updateStats();
      this.updateChartData();
      this.modalLoading = false;
      this.closeModal();
    }, 1000);
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChanged(target.value);
  }

  onSearchChanged(searchTerm: string): void {
    this.applyFilters();
  }

  onFilterChanged(filterType: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    // Apply filter logic here
    this.applyFilters();
  }

  onSortChanged(sort: any): void {
    // Implement sorting logic
    console.log('Sort changed:', sort);
  }

  private applyFilters(): void {
    // Implement filtering logic
    this.filteredTechStack = [...this.techStack];
  }
}
