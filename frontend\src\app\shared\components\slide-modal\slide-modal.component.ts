import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../button/button.component';

@Component({
  selector: 'app-slide-modal',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <!-- Modal Backdrop -->
    <div
      class="modal-backdrop"
      [class.show]="isOpen"
      (click)="onBackdropClick()"
      *ngIf="isOpen"
    ></div>

    <!-- Modal Panel -->
    <div
      class="modal-panel"
      [class.show]="isOpen"
      *ngIf="isOpen"
    >
      <!-- Modal Header -->
      <div class="modal-header">
        <div class="modal-title-section">
          <h2 class="modal-title">{{ title }}</h2>
          <p class="modal-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        </div>
        <button
          class="close-button"
          (click)="close()"
          type="button"
          aria-label="Close modal"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="modal-content">
        <ng-content></ng-content>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer" *ngIf="showFooter">
        <div class="footer-actions">
          <app-button
            variant="ghost"
            (clicked)="cancel()"
            [disabled]="loading"
          >
            {{ cancelText }}
          </app-button>
          <app-button
            variant="primary"
            (clicked)="confirm()"
            [loading]="loading"
            [disabled]="!canConfirm"
          >
            {{ confirmText }}
          </app-button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    /* Modal Backdrop */
    .modal-backdrop {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9998;
      opacity: 0;
      transition: opacity 0.3s ease;

      &.show {
        opacity: 1;
      }
    }

    /* Modal Panel */
    .modal-panel {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      width: 600px;
      max-width: 90vw;
      background: white;
      box-shadow: var(--shadow-xl);
      z-index: 9999;
      display: flex;
      flex-direction: column;
      transform: translateX(100%);
      transition: transform 0.3s ease;

      &.show {
        transform: translateX(0);
      }
    }

    /* Modal Header */
    .modal-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
      border-bottom: 1px solid var(--secondary-200);
      flex-shrink: 0;
    }

    .modal-title-section {
      flex: 1;
      margin-right: var(--spacing-md);
    }

    .modal-title {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--secondary-900);
      line-height: var(--leading-tight);
    }

    .modal-subtitle {
      margin: 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .close-button {
      background: none;
      border: none;
      color: var(--secondary-400);
      cursor: pointer;
      padding: var(--spacing-sm);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;
      flex-shrink: 0;

      &:hover {
        color: var(--secondary-600);
        background: var(--secondary-100);
      }

      svg {
        width: 20px;
        height: 20px;
      }
    }

    /* Modal Content */
    .modal-content {
      flex: 1;
      overflow-y: auto;
      padding: var(--spacing-lg) var(--spacing-xl);
    }

    /* Modal Footer */
    .modal-footer {
      padding: var(--spacing-lg) var(--spacing-xl);
      border-top: 1px solid var(--secondary-200);
      flex-shrink: 0;
    }

    .footer-actions {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-md);
    }

    /* Responsive */
    @media (max-width: 768px) {
      .modal-panel {
        width: 100vw;
        max-width: none;
      }

      .modal-header {
        padding: var(--spacing-lg);
      }

      .modal-content {
        padding: var(--spacing-md) var(--spacing-lg);
      }

      .modal-footer {
        padding: var(--spacing-lg);
      }

      .footer-actions {
        flex-direction: column-reverse;
        gap: var(--spacing-sm);
      }

      .footer-actions app-button {
        width: 100%;
      }
    }
  `]
})
export class SlideModalComponent implements OnInit, OnDestroy {
  @Input() isOpen = false;
  @Input() title = '';
  @Input() subtitle = '';
  @Input() showFooter = true;
  @Input() confirmText = 'Save';
  @Input() cancelText = 'Cancel';
  @Input() loading = false;
  @Input() canConfirm = true;
  @Input() closeOnBackdrop = true;

  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  @Output() confirmed = new EventEmitter<void>();
  @Output() cancelled = new EventEmitter<void>();

  ngOnInit(): void {
    if (this.isOpen) {
      this.handleOpen();
    }
  }

  ngOnDestroy(): void {
    this.handleClose();
  }

  ngOnChanges(): void {
    if (this.isOpen) {
      this.handleOpen();
    } else {
      this.handleClose();
    }
  }

  private handleOpen(): void {
    document.body.style.overflow = 'hidden';
    this.opened.emit();
  }

  private handleClose(): void {
    document.body.style.overflow = '';
    this.closed.emit();
  }

  onBackdropClick(): void {
    if (this.closeOnBackdrop) {
      this.close();
    }
  }

  close(): void {
    this.closed.emit();
  }

  confirm(): void {
    this.confirmed.emit();
  }

  cancel(): void {
    this.cancelled.emit();
  }
}
